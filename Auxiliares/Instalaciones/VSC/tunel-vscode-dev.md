# Tutorial: Configurar VS Code Tunnels para conectar código local con vscode.dev

## Introducción

VS Code Tunnels te permite acceder a tu código local desde cualquier lugar usando vscode.dev en el navegador. Esto es especialmente útil para:
- Trabajar desde diferentes dispositivos
- Acceder a tu entorno de desarrollo desde cualquier navegador
- Colaborar remotamente manteniendo tu configuración local

## Requisitos previos

- VS Code instalado localmente
- Conexión a internet estable
- Cuenta de Microsoft/GitHub (para autenticación)

## Paso 1: Verificar instalación de VS Code

Verificamos que VS Code esté instalado correctamente:

```bash
# Para VS Code estable
code --version

# Para VS Code Insiders (nuestro caso)
code-insiders --version
```

✅ **Detectado**: VS Code Insiders versión 1.103.0 instalado correctamente

## Paso 2: Configurar el túnel

### 2.1 Crear el túnel desde la línea de comandos

Abre una terminal en tu directorio de proyecto y ejecuta:

```bash
# Para VS Code Insiders
code-insiders tunnel
```

Este comando:
1. Te pedirá autenticarte con tu cuenta de Microsoft/GitHub
2. Creará un túnel con un nombre único
3. Mostrará una URL para acceder desde vscode.dev

### 2.2 Configurar nombre personalizado del túnel (opcional)

Para usar un nombre específico para tu túnel:

```bash
code-insiders tunnel --name mi-proyecto-matematicas-icfes
```

## Paso 3: Autenticación

### ✅ **COMPLETADO**: Autenticación con GitHub

La autenticación se completó exitosamente. Pasos realizados:

1. ✅ Acceso a: https://github.com/login/device
2. ✅ Código ingresado: **3946-E039**
3. ✅ VS Code Server autorizado
4. ✅ Túnel creado con nombre: **icfes-math**

**Nota importante**: Los nombres de túnel deben tener máximo 20 caracteres.

## Paso 4: Acceder desde vscode.dev

### ✅ **TÚNEL ACTIVO**: Acceso directo disponible

**URL directa de tu túnel**:
```
https://insiders.vscode.dev/tunnel/icfes-math/home/<USER>/Insync/<EMAIL>/Google%20Drive/RepositorioMatematicasICFES_R_Exams
```

**Otras formas de acceder**:
- https://insiders.vscode.dev y seleccionar "Connect to Tunnel" → buscar "icfes-math"
- https://vscode.dev/?vscode-lang=es-419 (en español) y conectar al túnel

## Instalación de VS Code (si no está instalado)

### Método 1: Descarga directa
```bash
# Descargar el paquete .deb
wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > packages.microsoft.gpg
sudo install -o root -g root -m 644 packages.microsoft.gpg /etc/apt/trusted.gpg.d/
sudo sh -c 'echo "deb [arch=amd64,arm64,armhf signed-by=/etc/apt/trusted.gpg.d/packages.microsoft.gpg] https://packages.microsoft.com/repos/code stable main" > /etc/apt/sources.list.d/vscode.list'

# Actualizar e instalar
sudo apt update
sudo apt install code
```

### Método 2: Snap
```bash
sudo snap install --classic code
```

## Estado actual de la configuración

- [x] VS Code instalado y verificado (✅ VS Code Insiders 1.103.0)
- [x] Túnel creado (✅ Nombre: icfes-math)
- [x] Autenticación completada (✅ GitHub autorizado)
- [x] Acceso desde vscode.dev verificado (✅ Funcionando perfectamente)

## Comandos útiles para VS Code Insiders

```bash
# Crear túnel
code-insiders tunnel

# Crear túnel con nombre específico
code-insiders tunnel --name nombre-personalizado

# Ver túneles existentes
code-insiders tunnel status

# Eliminar túnel
code-insiders tunnel unregister
```

## Información importante del túnel

### Detalles de la configuración
- **Nombre del túnel**: icfes-math
- **Tipo**: VS Code Insiders
- **Directorio base**: `/home/<USER>/Insync/<EMAIL>/Google Drive/RepositorioMatematicasICFES_R_Exams`
- **URL de acceso**: https://insiders.vscode.dev/tunnel/icfes-math/...

### Gestión del túnel

**Para mantener el túnel activo**:
- El proceso debe seguir ejecutándose en la terminal
- Si cierras la terminal, el túnel se desconectará
- Para ejecutar en segundo plano: `nohup code-insiders tunnel --name icfes-math &`

**Para detener el túnel**:
- Presiona `Ctrl+C` en la terminal donde está ejecutándose
- O usa: `code-insiders tunnel unregister`

## Próximos pasos recomendados

1. ✅ Verificar instalación de VS Code
2. ✅ Configurar el túnel
3. ✅ Probar la conexión desde vscode.dev
4. [ ] Configurar extensiones necesarias (R, Python, etc.)
5. [ ] Configurar sincronización de configuraciones
6. [ ] Probar edición de archivos del repositorio

## 🎉 ¡Configuración exitosa!

Tu túnel VS Code está funcionando perfectamente. Ahora puedes:

- **Editar código** desde cualquier navegador
- **Acceder a tu repositorio** de matemáticas ICFES remotamente
- **Mantener tu configuración local** mientras trabajas desde la web
- **Colaborar** compartiendo la URL del túnel (con permisos)

## Consejos adicionales

### Extensiones recomendadas para tu proyecto
- **R Extension for Visual Studio Code**: Para archivos .R y .Rmd
- **Python**: Para scripts de Python
- **LaTeX Workshop**: Para documentos LaTeX/TikZ
- **GitLens**: Para mejor integración con Git

### Seguridad
- El túnel está vinculado a tu cuenta de GitHub
- Solo tú puedes acceder a través de tu autenticación
- Para compartir acceso, usa las funciones de colaboración de VS Code

### Rendimiento
- La velocidad depende de tu conexión a internet
- Los archivos se editan localmente pero se sincronizan a través del túnel
- Para proyectos grandes, considera usar VS Code local cuando sea posible

---

*Tutorial creado: 2025-07-29*
*Configuración completada exitosamente: ✅ TÚNEL ACTIVO*
*Nombre del túnel: icfes-math*
